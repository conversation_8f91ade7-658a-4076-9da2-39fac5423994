"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ChatbotService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const axios_1 = require("axios");
const TABLES = [
    { prisma: 'user', keywords: ['user', 'users', 'utilisateur', 'utilisateurs', 'compte', 'email', 'profil'], label: 'name' },
    { prisma: 'program', keywords: ['program', 'programs', 'programme', 'formation', 'cours', 'programmes'], label: 'name' },
    { prisma: 'module', keywords: ['module', 'modules', 'chapitre', 'section'], label: 'name' },
    { prisma: 'course', keywords: ['course', 'courses', 'cours', 'leçon'], label: 'name' },
    { prisma: 'contenu', keywords: ['contenu', 'contenus', 'document', 'ressource', 'fichier'], label: 'title' },
    { prisma: 'quiz', keywords: ['quiz', 'quizzes', 'test', 'évaluation', 'question'], label: 'title' },
];
const DEFAULT_RESPONSES = {
    greeting: "Bonjour ! Je suis l'assistant de la plateforme LMS. Comment puis-je vous aider aujourd'hui ?",
    help: "Je peux vous aider avec des informations sur les utilisateurs, programmes, modules, cours, contenus et quiz. Vous pouvez me demander par exemple 'combien d'utilisateurs', 'liste des programmes', 'détail d'un cours', etc.",
    notUnderstood: "Je ne suis pas sûr de comprendre votre demande. Pourriez-vous reformuler ou me poser une question sur les utilisateurs, programmes, modules ou cours ?",
    error: "Je ne peux pas répondre à cette question pour le moment. Pourriez-vous me demander des informations sur la base de données, comme le nombre d'utilisateurs ou la liste des programmes ?"
};
function normalize(str) {
    return str
        .replace(/[''`]/g, "'")
        .replace(/\s+/g, ' ')
        .toLowerCase();
}
let ChatbotService = ChatbotService_1 = class ChatbotService {
    prisma;
    logger = new common_1.Logger(ChatbotService_1.name);
    groqApiKey = process.env.GROQ_API_KEY;
    groqModel = 'llama-3.3-70b-versatile';
    constructor(prisma) {
        this.prisma = prisma;
    }
    async processMessage(message) {
        try {
            const lowerMsg = normalize(message);
            if (['hello', 'bonjour', 'salut', 'hi', 'hey'].some(greeting => lowerMsg.includes(greeting))) {
                return DEFAULT_RESPONSES.greeting;
            }
            if (['aide', 'help', 'aidez-moi', 'que peux-tu faire', 'what can you do'].some(help => lowerMsg.includes(help))) {
                return DEFAULT_RESPONSES.help;
            }
            if (lowerMsg.includes('select') && lowerMsg.includes('from')) {
                const results = await this.executeQuery(message);
                return `Résultats de la requête: ${JSON.stringify(results, null, 2)}`;
            }
            for (const { prisma, keywords } of TABLES) {
                for (const k of keywords) {
                    if (lowerMsg.match(new RegExp(`combien d['']? ?${k}\\b`)) ||
                        lowerMsg.match(new RegExp(`combien de ${k}\\b`)) ||
                        lowerMsg.match(new RegExp(`nombre de ${k}\\b`))) {
                        const count = await this.prisma[prisma].count();
                        return `Il y a actuellement ${count} ${prisma}s dans la base de données.`;
                    }
                    if (lowerMsg.match(new RegExp(`how many[\\w\\s]*${k}\\b`))) {
                        const count = await this.prisma[prisma].count();
                        return `There are currently ${count} ${prisma}s in this application.`;
                    }
                }
            }
            for (const { prisma, keywords } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('liste des ' + k) || lowerMsg.includes('list of ' + k) || lowerMsg.includes('affiche les ' + k) || lowerMsg.includes('montre les ' + k))) {
                    const items = await this.prisma[prisma].findMany({ take: 10 });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Voici quelques ${prisma}s :\n` + items.map(i => JSON.stringify(i)).join('\n');
                }
            }
            for (const { prisma, keywords, label } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('nom de ' + k) || lowerMsg.includes('noms de ' + k) || lowerMsg.includes('names of ' + k))) {
                    const items = await this.prisma[prisma].findMany({ take: 20, select: { [label]: true } });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Voici les noms des ${prisma}s :\n` + items.map(i => i[label]).filter(Boolean).join(', ');
                }
            }
            for (const { prisma, keywords } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('détail') && lowerMsg.includes(k))) {
                    const items = await this.prisma[prisma].findMany({ take: 1 });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Détail d'un(e) ${prisma} :\n` + JSON.stringify(items[0], null, 2);
                }
            }
            try {
                const groqResponse = await this.askGroq(message);
                if (groqResponse && groqResponse.trim() !== '') {
                    return groqResponse;
                }
            }
            catch (error) {
                this.logger.error(`Erreur Groq: ${error.message}`);
            }
            return DEFAULT_RESPONSES.notUnderstood;
        }
        catch (error) {
            this.logger.error(`Erreur lors du traitement du message: ${error.message}`);
            return DEFAULT_RESPONSES.error;
        }
    }
    async askGroq(message) {
        try {
            if (!this.groqApiKey) {
                this.logger.warn('Clé API Groq non configurée');
                return null;
            }
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    { role: 'system', content: 'Tu es un assistant intelligent pour une plateforme LMS. Réponds de façon concise et précise.' },
                    { role: 'user', content: message }
                ],
                temperature: 0.7,
                max_tokens: 1000,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 15000,
            });
            return response.data?.choices?.[0]?.message?.content || null;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'appel à Groq: ${error.message}`);
            if (error.response) {
                this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
            }
            return null;
        }
    }
    async executeQuery(query) {
        try {
            if (!query.toLowerCase().trim().startsWith('select')) {
                throw new Error('Seules les requêtes SELECT sont autorisées');
            }
            const results = await this.prisma.$queryRawUnsafe(query);
            return results;
        }
        catch (error) {
            this.logger.error(`Erreur SQL: ${error.message}`);
            return { error: error.message };
        }
    }
};
exports.ChatbotService = ChatbotService;
exports.ChatbotService = ChatbotService = ChatbotService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], ChatbotService);
//# sourceMappingURL=chatbot.service.js.map